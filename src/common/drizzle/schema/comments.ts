import { datetime, int, mysqlTable, text, varchar } from 'drizzle-orm/mysql-core'
import { relations } from 'drizzle-orm'
import KSUID from 'ksuid'
import { dramas } from './dramas'
import { systemUsers } from './system-users'

// 评论表
export const comments = mysqlTable('comments', {
  id: varchar('id', { length: 100 })
    .notNull()
    .primaryKey()
    .$defaultFn(() => KSUID.randomSync().string),
  userId: varchar('userId', { length: 100 })
    .notNull()
    .references(() => systemUsers.id), // 评论用户ID
  dramaId: varchar('dramaId', { length: 100 })
    .notNull()
    .references(() => dramas.id), // 关联短剧ID
  content: text('content').notNull(), // 评论内容
  parentId: varchar('parent_id', { length: 100 }), // 父评论ID，用于回复功能
  replyToUserId: varchar('reply_to_user_id', { length: 100 }), // 回复目标用户ID
  likeCount: int('like_count').default(0), // 点赞数
  replyCount: int('reply_count').default(0), // 回复数
  status: int('status').notNull().default(1), // 评论状态：0-待审核 1-已发布 2-已隐藏
  isFeatured: int('is_featured').notNull().default(0), // 是否精选：0-否 1-是
  createdAt: datetime('created_at').$default(() => new Date()),
  updatedAt: datetime('updated_at')
    .$default(() => new Date())
    .$onUpdate(() => new Date()),
  isDeleted: int('is_deleted').notNull().default(0), // 软删除标记 0:未删除 1:已删除
})

// 定义评论与用户、短剧的关系
export const commentsRelations = relations(comments, ({ one }) => ({
  user: one(systemUsers, {
    fields: [comments.userId],
    references: [systemUsers.id],
  }),
  drama: one(dramas, {
    fields: [comments.dramaId],
    references: [dramas.id],
  }),
  replyToUser: one(systemUsers, {
    fields: [comments.replyToUserId],
    references: [systemUsers.id],
  }),
}))

import { Module } from '@nestjs/common'
import { DouyinService } from './services/douyin.service'
import { DouyinController } from './controllers/douyin.controller'
import { DouyinCategoryService } from './services/category.service'
import { CategoryController } from './controllers/category.controller'
import { SystemModule } from '@/modules/system/system.module'

@Module({
  imports: [SystemModule],
  controllers: [DouyinController, CategoryController],
  providers: [DouyinService, DouyinCategoryService],
})
export class DouyinModule {}

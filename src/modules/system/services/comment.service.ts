import { Injectable } from '@nestjs/common'
import { DrizzleService } from '@/common/drizzle/database.provider'
import { comments } from '@/common/drizzle/schema'
import { eq, and, like, SQL } from 'drizzle-orm'
import { pageQuery } from '@/common/utils/page-query.util'
import { toResponse } from '@/common/utils/transform.util'
import { PaginationRequest } from '@/common/requests/pagination.request'
import { PaginationResponse } from '@/common/responses/pagination.response'
import { LogicDelete } from '@/constants/system.constants'
import {
  SystemCommentCreateRequest,
  SystemCommentUpdateRequest,
  SystemCommentFilterRequest,
} from '../requests/comment.request'
import { SystemCommentResponse } from '../responses/comment.response'

@Injectable()
export class SystemCommentService {
  constructor(private readonly drizzle: DrizzleService) {}

  async findAll(pagination: PaginationRequest, filter: SystemCommentFilterRequest) {
    const buildCondition = (): SQL | undefined => {
      const wheres: SQL[] = [eq(comments.isDeleted, LogicDelete.NotDeleted)]

      if (filter.userId) wheres.push(eq(comments.userId, filter.userId))
      if (filter.dramaId) wheres.push(eq(comments.dramaId, filter.dramaId))
      if (filter.parentId) wheres.push(eq(comments.parentId, filter.parentId))
      if (filter.status !== undefined) wheres.push(eq(comments.status, filter.status))
      if (filter.content) wheres.push(like(comments.content, `%${filter.content}%`))

      return wheres.length ? and(...wheres) : undefined
    }

    const { getData } = pageQuery(this.drizzle.db, comments, pagination, {
      condition: buildCondition,
    })
    const { data, total } = await getData()
    const list = toResponse(SystemCommentResponse, data)
    return PaginationResponse.fromPaginationRequest(list, total, pagination)
  }

  async findOne(id: string) {
    const comment = await this.drizzle.db.query.comments.findFirst({
      where: (comments, { eq, and }) => and(eq(comments.id, id), eq(comments.isDeleted, LogicDelete.NotDeleted)),
      with: {
        user: {
          columns: {
            id: true,
            username: true,
            nickname: true,
          },
        },
        drama: {
          columns: {
            id: true,
            title: true,
          },
        },
        replyToUser: {
          columns: {
            id: true,
            username: true,
            nickname: true,
          },
        },
      },
    })
    return comment ? toResponse(SystemCommentResponse, comment) : null
  }

  async create(dto: SystemCommentCreateRequest) {
    const [{ id }] = await this.drizzle.db.insert(comments).values(dto).$returningId()
    return id
  }

  async update(id: string, dto: SystemCommentUpdateRequest) {
    const [{ affectedRows }] = await this.drizzle.db
      .update(comments)
      .set(dto)
      .where(and(eq(comments.id, id), eq(comments.isDeleted, LogicDelete.NotDeleted)))
    return affectedRows > 0
  }

  async delete(id: string) {
    const [{ affectedRows }] = await this.drizzle.db
      .update(comments)
      .set({ isDeleted: LogicDelete.Deleted })
      .where(eq(comments.id, id))
    return affectedRows > 0
  }
}

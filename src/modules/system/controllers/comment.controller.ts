import { Auth } from '@/common/decorators/auth.decorator'
import { CommonResponse } from '@/common/responses/common.response'
import { Body, Controller, Delete, Get, Param, Post, Put, Query } from '@nestjs/common'
import { ApiOperation, ApiTags } from '@nestjs/swagger'
import { SystemCommentService } from '../services/comment.service'
import { PaginationRequest } from '@/common/requests/pagination.request'
import {
  SystemCommentCreateRequest,
  SystemCommentUpdateRequest,
  SystemCommentFilterRequest,
} from '../requests/comment.request'

@ApiTags('评论管理')
@Auth()
@Controller('/admin/comment')
export class SystemCommentController {
  constructor(private readonly systemCommentService: SystemCommentService) {}

  @ApiOperation({ summary: '获取评论列表' })
  @Get()
  async findAll(@Query() pagination: PaginationRequest, @Query() filter: SystemCommentFilterRequest) {
    const result = await this.systemCommentService.findAll(pagination, filter)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '获取评论详情' })
  @Get(':id')
  async findOne(@Param('id') id: string) {
    const result = await this.systemCommentService.findOne(id)
    return CommonResponse.ok(result)
  }

  @ApiOperation({ summary: '创建评论' })
  @Post()
  async create(@Body() dto: SystemCommentCreateRequest) {
    const id = await this.systemCommentService.create(dto)
    return CommonResponse.ok({ id })
  }

  @ApiOperation({ summary: '更新评论' })
  @Put(':id')
  async update(@Param('id') id: string, @Body() dto: SystemCommentUpdateRequest) {
    const ok = await this.systemCommentService.update(id, dto)
    return CommonResponse.ok({ ok })
  }

  @ApiOperation({ summary: '删除评论' })
  @Delete(':id')
  async delete(@Param('id') id: string) {
    const ok = await this.systemCommentService.delete(id)
    return CommonResponse.ok({ ok })
  }
}

import { IsInt, <PERSON>NotEmpty, <PERSON>Optional, IsString, MaxLength, Min } from 'class-validator'

export class SystemCommentCreateRequest {
  @IsNotEmpty({ message: '用户ID不能为空' })
  @IsString()
  @MaxLength(100)
  userId: string

  @IsNotEmpty({ message: '短剧ID不能为空' })
  @IsString()
  @MaxLength(100)
  dramaId: string

  @IsNotEmpty({ message: '评论内容不能为空' })
  @IsString()
  @MaxLength(1000)
  content: string

  @IsOptional()
  @IsString()
  @MaxLength(100)
  parentId?: string

  @IsOptional()
  @IsString()
  @MaxLength(100)
  replyToUserId?: string

  @IsOptional()
  @IsInt()
  @Min(0)
  likeCount?: number

  @IsOptional()
  @IsInt()
  @Min(0)
  replyCount?: number

  @IsOptional()
  @IsInt()
  status?: number
}

export class SystemCommentUpdateRequest {
  @IsOptional()
  @IsString()
  @MaxLength(1000)
  content?: string

  @IsOptional()
  @IsInt()
  @Min(0)
  likeCount?: number

  @IsOptional()
  @IsInt()
  @Min(0)
  replyCount?: number

  @IsOptional()
  @IsInt()
  status?: number
}

export class SystemCommentFilterRequest {
  @IsOptional()
  @IsString()
  userId?: string

  @IsOptional()
  @IsString()
  dramaId?: string

  @IsOptional()
  @IsString()
  parentId?: string

  @IsOptional()
  @IsInt()
  status?: number

  @IsOptional()
  @IsString()
  content?: string
}
